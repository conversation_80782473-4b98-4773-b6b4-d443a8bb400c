 forge test
maakaijs@DESKTOP-F3VVSO0:~/kulu/kuru-contracts$ forge test
[⠒] Compiling...
No files changed, compilation skipped

Ran 1 test for test/test_benchmarks/BenchMarkPhoenix.t.sol:BenchMarkTest
[FAIL: vm.ffi: FFI is disabled; add the `--ffi` flag to allow tests to call external commands] constructor() (gas: 0)
Suite result: FAILED. 0 passed; 1 failed; 0 skipped; finished in 855.90µs (0.00ns CPU time)

Ran 1 test for test/test_benchmarks/BenchMarkingStorage.sol:BenchMarkTest
[FAIL: vm.ffi: FFI is disabled; add the `--ffi` flag to allow tests to call external commands] constructor() (gas: 0)
Suite result: FAILED. 0 passed; 1 failed; 0 skipped; finished in 1.30ms (0.00ns CPU time)

Ran 6 tests for test/MonadDeployer.t.sol:MonadDeployerTest
[PASS] testDeployMarket() (gas: 2028447)
[PASS] testDeployMarketRevertInsufficientAssets() (gas: 1752879)
[PASS] testRevertSetKuruAmmSpreadNotOwner() (gas: 11323)
[PASS] testRevertSetKuruCollectiveNotOwner() (gas: 11369)
[PASS] testSetKuruAmmSpread() (gas: 11319)
[PASS] testSetKuruCollective() (gas: 11023)
Suite result: ok. 6 passed; 0 failed; 0 skipped; finished in 4.82ms (2.02ms CPU time)

Ran 8 tests for test/RouterTest.t.sol:OrderBookTest
[PASS] testAnyToAnySwap() (gas: 1445147)
[PASS] testMultiMarketNativeToTokenSwap() (gas: 1168903)
[PASS] testMultiRouteSwapEndingInNative() (gas: 772081)
[PASS] testNativeBuyInMonNativeMarket() (gas: 441935)
[PASS] testNativeToTokenSwap() (gas: 809044)
[PASS] testRevertInvalidMarket() (gas: 692463)
[PASS] testRevertLengthMismatch() (gas: 646770)
[PASS] testRevertSlippageExceeded() (gas: 933748)
Suite result: ok. 8 passed; 0 failed; 0 skipped; finished in 24.38ms (17.73ms CPU time)

Ran 1 test for test/TestEncoding.t.sol:EncodingTest
[PASS] testEncodeDecode() (gas: 13976)
Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 429.20µs (228.60µs CPU time)
